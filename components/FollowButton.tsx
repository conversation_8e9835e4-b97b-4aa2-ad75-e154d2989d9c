"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"

interface FollowButtonProps {
  writerId: string
  writerName?: string
  initialIsFollowing?: boolean
  size?: 'sm' | 'md' | 'lg'
}

export function FollowButton({ writerId, writerName, initialIsFollowing = false, size = 'md' }: FollowButtonProps) {
  const [loading, setLoading] = useState(false)
  const [isFollowing, setIsFollowing] = useState(initialIsFollowing)
  const router = useRouter()

  const handleFollowToggle = async (e: React.MouseEvent<HTMLButtonElement>) => {
    e.preventDefault()
    setLoading(true)
    try {
      const response = await fetch('/api/follow-dev', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ 
          writerId, 
          action: isFollowing ? 'unfollow' : 'follow' 
        }),
      })
      const data = await response.json()
      
      if (data.success) {
        setIsFollowing(data.isFollowing ?? !isFollowing)
        alert(data.message || `Successfully ${isFollowing ? 'unfollowed' : 'followed'} ${writerName || 'writer'}!`)
        // Force a hard refresh to update follower counts
        window.location.reload()
      } else {
        alert(data.error || `Failed to ${isFollowing ? 'unfollow' : 'follow'}`)
      }
    } catch (error) {
      console.error('Follow error:', error)
      alert(`Failed to ${isFollowing ? 'unfollow' : 'follow'}`)
    } finally {
      setLoading(false)
    }
  }

  const sizeClasses = {
    sm: 'px-3 py-1 text-xs rounded-md',
    md: 'px-6 py-2 text-sm rounded-lg',
    lg: 'px-8 py-3 text-base rounded-lg'
  }

  return (
    <button
      onClick={(e) => handleFollowToggle(e)}
      disabled={loading}
      className={`font-medium transition-colors disabled:opacity-50 ${sizeClasses[size]} ${
        isFollowing
          ? 'bg-gray-200 text-gray-700 hover:bg-gray-300'
          : 'bg-blue-600 text-white hover:bg-blue-700'
      }`}
    >
      {loading
        ? (isFollowing ? 'Unfollowing...' : 'Following...')
        : (isFollowing ? 'Unfollow' : 'Follow')
      }
    </button>
  )
}
