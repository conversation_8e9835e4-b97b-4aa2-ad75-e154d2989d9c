'use client'

import React, { useEffect, useState, useRef } from 'react'
import { useTutorial } from '@/contexts/TutorialContext'
import { Button } from '@/components/ui/button'
import { X, ChevronLeft, ChevronRight, SkipForward, RotateCcw } from 'lucide-react'
import { tutorialCategories } from '@/lib/tutorial-steps'

interface SpotlightPosition {
  top: number
  left: number
  width: number
  height: number
}

export function TutorialOverlay() {
  const {
    state,
    currentStepData,
    nextStep,
    previousStep,
    skipStep,
    stopTutorial,
    hideTutorial,
    getProgress
  } = useTutorial()

  const [spotlightPosition, setSpotlightPosition] = useState<SpotlightPosition | null>(null)
  const [isAnimating, setIsAnimating] = useState(false)
  const overlayRef = useRef<HTMLDivElement>(null)

  // Calculate spotlight position for target element
  useEffect(() => {
    if (!currentStepData?.target || !state.isActive || !state.isVisible) {
      setSpotlightPosition(null)
      return
    }

    const updateSpotlight = () => {
      const targetElement = document.querySelector(currentStepData.target!)
      if (!targetElement) {
        setSpotlightPosition(null)
        return
      }

      const rect = targetElement.getBoundingClientRect()
      const padding = 8 // Extra space around the element
      
      setSpotlightPosition({
        top: rect.top - padding,
        left: rect.left - padding,
        width: rect.width + (padding * 2),
        height: rect.height + (padding * 2)
      })
    }

    updateSpotlight()
    
    // Update on scroll and resize
    const handleUpdate = () => updateSpotlight()
    window.addEventListener('scroll', handleUpdate)
    window.addEventListener('resize', handleUpdate)
    
    return () => {
      window.removeEventListener('scroll', handleUpdate)
      window.removeEventListener('resize', handleUpdate)
    }
  }, [currentStepData?.target, state.isActive, state.isVisible])

  // Handle step transitions with animation
  const handleNextStep = () => {
    setIsAnimating(true)
    setTimeout(() => {
      nextStep()
      setIsAnimating(false)
    }, 150)
  }

  const handlePreviousStep = () => {
    setIsAnimating(true)
    setTimeout(() => {
      previousStep()
      setIsAnimating(false)
    }, 150)
  }

  // Don't render if tutorial is not active or visible
  if (!state.isActive || !state.isVisible || !currentStepData) {
    return null
  }

  const progress = getProgress()
  const currentCategory = tutorialCategories.find(cat => cat.id === currentStepData.category)

  // Calculate tooltip position
  const getTooltipPosition = () => {
    if (!spotlightPosition) {
      return { top: '50%', left: '50%', transform: 'translate(-50%, -50%)' }
    }

    const { top, left, width, height } = spotlightPosition
    const tooltipWidth = 320
    const tooltipHeight = 200
    const margin = 20

    let tooltipTop = top
    let tooltipLeft = left + width + margin
    let transform = 'translateY(-50%)'

    // Adjust position based on currentStepData.position
    switch (currentStepData.position) {
      case 'top':
        tooltipTop = top - tooltipHeight - margin
        tooltipLeft = left + (width / 2)
        transform = 'translateX(-50%)'
        break
      case 'bottom':
        tooltipTop = top + height + margin
        tooltipLeft = left + (width / 2)
        transform = 'translateX(-50%)'
        break
      case 'left':
        tooltipTop = top + (height / 2)
        tooltipLeft = left - tooltipWidth - margin
        transform = 'translateY(-50%)'
        break
      case 'right':
        tooltipTop = top + (height / 2)
        tooltipLeft = left + width + margin
        transform = 'translateY(-50%)'
        break
      case 'center':
        tooltipTop = window.innerHeight / 2
        tooltipLeft = window.innerWidth / 2
        transform = 'translate(-50%, -50%)'
        break
    }

    // Ensure tooltip stays within viewport
    if (tooltipLeft < margin) tooltipLeft = margin
    if (tooltipLeft + tooltipWidth > window.innerWidth - margin) {
      tooltipLeft = window.innerWidth - tooltipWidth - margin
    }
    if (tooltipTop < margin) tooltipTop = margin
    if (tooltipTop + tooltipHeight > window.innerHeight - margin) {
      tooltipTop = window.innerHeight - tooltipHeight - margin
    }

    return {
      top: `${tooltipTop}px`,
      left: `${tooltipLeft}px`,
      transform
    }
  }

  const tooltipStyle = getTooltipPosition()

  return (
    <div
      ref={overlayRef}
      className="fixed inset-0 z-[9990] pointer-events-none"
      style={{ zIndex: 9990 }}
    >
      {/* Dark overlay with spotlight cutout */}
      <div className="absolute inset-0 bg-black bg-opacity-60 transition-opacity duration-300">
        {spotlightPosition && (
          <div
            className="absolute bg-transparent border-4 border-white border-opacity-30 rounded-lg shadow-2xl transition-all duration-300 ease-out"
            style={{
              top: `${spotlightPosition.top}px`,
              left: `${spotlightPosition.left}px`,
              width: `${spotlightPosition.width}px`,
              height: `${spotlightPosition.height}px`,
              boxShadow: `
                0 0 0 4px rgba(255, 255, 255, 0.3),
                0 0 0 9999px rgba(0, 0, 0, 0.6),
                inset 0 0 20px rgba(255, 255, 255, 0.1)
              `
            }}
          />
        )}
      </div>

      {/* Tutorial tooltip */}
      <div
        className={`
          absolute pointer-events-auto bg-white rounded-xl shadow-2xl border border-gray-200 
          transition-all duration-300 ease-out max-w-sm w-80
          ${isAnimating ? 'opacity-50 scale-95' : 'opacity-100 scale-100'}
        `}
        style={tooltipStyle}
      >
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-100">
          <div className="flex items-center gap-2">
            {currentCategory && (
              <span className="text-lg">{currentCategory.icon}</span>
            )}
            <div>
              <h3 className="font-semibold text-gray-900 text-sm">
                {currentStepData.title}
              </h3>
              {currentCategory && (
                <p className="text-xs text-gray-500">{currentCategory.name}</p>
              )}
            </div>
          </div>
          <button
            onClick={hideTutorial}
            className="text-gray-400 hover:text-gray-600 transition-colors p-1 rounded-md hover:bg-gray-100"
          >
            <X size={16} />
          </button>
        </div>

        {/* Content */}
        <div className="p-4">
          <p className="text-gray-700 text-sm leading-relaxed mb-4">
            {currentStepData.content}
          </p>

          {/* Progress bar */}
          <div className="mb-4">
            <div className="flex justify-between items-center mb-1">
              <span className="text-xs text-gray-500">Progress</span>
              <span className="text-xs text-gray-500">
                {progress.completed} of {progress.total}
              </span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-1.5">
              <div
                className="bg-gradient-to-r from-blue-500 to-purple-600 h-1.5 rounded-full transition-all duration-300"
                style={{ width: `${progress.percentage}%` }}
              />
            </div>
          </div>
        </div>

        {/* Footer with navigation */}
        <div className="flex items-center justify-between p-4 border-t border-gray-100 bg-gray-50 rounded-b-xl">
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={handlePreviousStep}
              disabled={state.currentStep === 0}
              className="text-xs px-2 py-1 h-7"
            >
              <ChevronLeft size={14} />
            </Button>
            
            <Button
              variant="outline"
              size="sm"
              onClick={skipStep}
              className="text-xs px-2 py-1 h-7"
            >
              <SkipForward size={14} />
            </Button>
          </div>

          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={stopTutorial}
              className="text-xs px-3 py-1 h-7"
            >
              Exit
            </Button>
            
            <Button
              size="sm"
              onClick={handleNextStep}
              className="text-xs px-3 py-1 h-7 bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700"
            >
              Next <ChevronRight size={14} className="ml-1" />
            </Button>
          </div>
        </div>
      </div>

      {/* Pulse animation for target element */}
      {spotlightPosition && currentStepData.action === 'click' && (
        <div
          className="absolute pointer-events-none animate-pulse"
          style={{
            top: `${spotlightPosition.top + spotlightPosition.height / 2 - 12}px`,
            left: `${spotlightPosition.left + spotlightPosition.width / 2 - 12}px`,
          }}
        >
          <div className="w-6 h-6 bg-blue-500 rounded-full opacity-60 animate-ping" />
          <div className="absolute top-1 left-1 w-4 h-4 bg-blue-400 rounded-full" />
        </div>
      )}
    </div>
  )
}
