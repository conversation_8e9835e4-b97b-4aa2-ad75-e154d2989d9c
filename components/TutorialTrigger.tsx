'use client'

import React, { useState, useEffect } from 'react'
import { useTutorial } from '@/contexts/TutorialContext'
import { Button } from '@/components/ui/button'
import {
  HelpCircle,
  X,
  Play,
  RotateCcw,
  BookOpen,
  Mic,
  User,
  Star,
  Layers,
  ChevronDown,
  ChevronUp,
  Settings
} from 'lucide-react'
import { tutorialCategories, getCategoryProgress } from '@/lib/tutorial-steps'
import { useTutorialSettings } from '@/components/TutorialSettings'

export function TutorialTrigger() {
  const {
    state,
    startTutorial,
    startCategory,
    toggleTutorial,
    updatePreferences,
    getProgress,
    isCategoryComplete,
    reset
  } = useTutorial()

  const [isExpanded, setIsExpanded] = useState(false)
  const [isVisible, setIsVisible] = useState(true)
  const [hasInteracted, setHasInteracted] = useState(false)
  const { openSettings, TutorialSettingsModal } = useTutorialSettings()

  // Auto-hide after initial period if user hasn't interacted
  useEffect(() => {
    if (!hasInteracted && state.userPreferences.showTutorial) {
      const timer = setTimeout(() => {
        if (!hasInteracted) {
          setIsVisible(false)
        }
      }, 10000) // Hide after 10 seconds if no interaction

      return () => clearTimeout(timer)
    }
  }, [hasInteracted, state.userPreferences.showTutorial])

  // Show tutorial trigger when user hasn't completed welcome
  useEffect(() => {
    if (!isCategoryComplete('welcome') && state.userPreferences.showTutorial) {
      setIsVisible(true)
    }
  }, [isCategoryComplete, state.userPreferences.showTutorial])

  const handleInteraction = () => {
    setHasInteracted(true)
    setIsVisible(true)
  }

  const handleStartTutorial = () => {
    handleInteraction()
    startTutorial('welcome')
    setIsExpanded(false)
  }

  const handleStartCategory = (category: string) => {
    handleInteraction()
    startCategory(category)
    setIsExpanded(false)
  }

  const handleToggleExpanded = () => {
    handleInteraction()
    setIsExpanded(!isExpanded)
  }

  const handleHideTutorial = () => {
    updatePreferences({ showTutorial: false })
    setIsVisible(false)
    setIsExpanded(false)
  }

  const handleShowAgain = () => {
    setIsVisible(true)
    handleInteraction()
  }

  const handleResetTutorial = () => {
    reset()
    setIsVisible(true)
    setHasInteracted(false)
    setIsExpanded(false)
  }

  const progress = getProgress()

  // Don't show if user has disabled tutorials - but always show a small trigger
  if (!state.userPreferences.showTutorial || !isVisible) {
    return (
      <button
        onClick={handleShowAgain}
        onTouchStart={handleShowAgain}
        onDoubleClick={handleResetTutorial}
        className="fixed bottom-4 right-4 z-[9995] w-8 h-12 bg-blue-500 hover:bg-blue-600 rounded-l-full transition-all duration-300 opacity-60 hover:opacity-90 shadow-lg min-h-[48px] min-w-[32px] flex items-center justify-center"
        style={{
          touchAction: 'manipulation',
          WebkitTapHighlightColor: 'transparent',
          userSelect: 'none',
          zIndex: 9995
        }}
        title="Show tutorial help (double-click to reset)"
      >
        <HelpCircle size={14} className="text-white ml-0.5" />
      </button>
    )
  }

  if (!isVisible) {
    return null
  }

  const getCategoryIcon = (categoryId: string) => {
    switch (categoryId) {
      case 'welcome': return <Star size={16} />
      case 'writing': return <BookOpen size={16} />
      case 'audio': return <Mic size={16} />
      case 'profile': return <User size={16} />
      case 'discovery': return <Star size={16} />
      case 'advanced': return <Layers size={16} />
      default: return <HelpCircle size={16} />
    }
  }

  const getCategoryColor = (categoryId: string) => {
    const category = tutorialCategories.find(cat => cat.id === categoryId)
    return category?.color || 'gray'
  }

  return (
    <div className="fixed bottom-4 right-4 z-[9995] flex flex-col items-end gap-2 sm:bottom-6 sm:right-6" style={{ zIndex: 9995 }}>
      {/* Expanded menu */}
      {isExpanded && (
        <div className="bg-white rounded-xl shadow-2xl border border-gray-200 p-4 w-80 mb-2 animate-in slide-in-from-bottom-2 duration-200">
          <div className="flex items-center justify-between mb-4">
            <div>
              <h3 className="font-semibold text-gray-900 text-sm">Tutorial Guide</h3>
              <p className="text-xs text-gray-500">
                {progress.completed} of {progress.total} steps completed
              </p>
            </div>
            <div className="flex gap-1">
              <button
                onClick={handleResetTutorial}
                className="text-gray-400 hover:text-gray-600 transition-colors p-1 rounded-md hover:bg-gray-100"
                title="Reset tutorial progress"
              >
                <RotateCcw size={16} />
              </button>
              <button
                onClick={() => {
                  openSettings()
                  setIsExpanded(false)
                }}
                className="text-gray-400 hover:text-gray-600 transition-colors p-1 rounded-md hover:bg-gray-100"
                title="Tutorial settings"
              >
                <Settings size={16} />
              </button>
              <button
                onClick={handleHideTutorial}
                className="text-gray-400 hover:text-gray-600 transition-colors p-1 rounded-md hover:bg-gray-100"
                title="Hide tutorial permanently"
              >
                <X size={16} />
              </button>
            </div>
          </div>

          {/* Progress bar */}
          <div className="mb-4">
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div
                className="bg-gradient-to-r from-blue-500 to-purple-600 h-2 rounded-full transition-all duration-300"
                style={{ width: `${progress.percentage}%` }}
              />
            </div>
          </div>

          {/* Quick start */}
          {!isCategoryComplete('welcome') && (
            <div className="mb-4 p-3 bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg border border-blue-200">
              <h4 className="font-medium text-blue-900 text-sm mb-1">New to OnlyDiary?</h4>
              <p className="text-xs text-blue-700 mb-2">Start with our welcome tour</p>
              <Button
                size="sm"
                onClick={handleStartTutorial}
                className="text-xs h-7 bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700"
              >
                <Play size={12} className="mr-1" />
                Start Tour
              </Button>
            </div>
          )}

          {/* Category list */}
          <div className="space-y-2">
            <h4 className="font-medium text-gray-700 text-xs uppercase tracking-wide">
              Tutorial Categories
            </h4>
            {tutorialCategories.map((category) => {
              const categoryProgress = getCategoryProgress(category.id, state.completedSteps)
              const isComplete = isCategoryComplete(category.id)
              
              return (
                <button
                  key={category.id}
                  onClick={() => handleStartCategory(category.id)}
                  className={`
                    w-full flex items-center justify-between p-2 rounded-lg transition-all duration-200
                    ${isComplete 
                      ? 'bg-green-50 border border-green-200 hover:bg-green-100' 
                      : 'bg-gray-50 border border-gray-200 hover:bg-gray-100'
                    }
                  `}
                >
                  <div className="flex items-center gap-2">
                    <div className={`
                      w-6 h-6 rounded-full flex items-center justify-center text-white text-xs
                      ${isComplete ? 'bg-green-500' : `bg-${getCategoryColor(category.id)}-500`}
                    `}>
                      {isComplete ? '✓' : getCategoryIcon(category.id)}
                    </div>
                    <div className="text-left">
                      <p className="text-sm font-medium text-gray-900">{category.name}</p>
                      <p className="text-xs text-gray-500">{category.description}</p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="text-xs text-gray-500">
                      {categoryProgress.completed}/{categoryProgress.total}
                    </p>
                    <div className="w-12 bg-gray-200 rounded-full h-1 mt-1">
                      <div
                        className={`h-1 rounded-full transition-all duration-300 ${
                          isComplete ? 'bg-green-500' : `bg-${getCategoryColor(category.id)}-500`
                        }`}
                        style={{ width: `${categoryProgress.percentage}%` }}
                      />
                    </div>
                  </div>
                </button>
              )
            })}
          </div>

          {/* Resume current tutorial */}
          {state.isActive && (
            <div className="mt-4 p-3 bg-blue-50 rounded-lg border border-blue-200">
              <h4 className="font-medium text-blue-900 text-sm mb-1">Tutorial in Progress</h4>
              <p className="text-xs text-blue-700 mb-2">Continue where you left off</p>
              <Button
                size="sm"
                onClick={() => {
                  toggleTutorial()
                  setIsExpanded(false)
                }}
                variant="outline"
                className="text-xs h-7 border-blue-300 text-blue-700 hover:bg-blue-100"
              >
                Resume Tutorial
              </Button>
            </div>
          )}
        </div>
      )}

      {/* Main trigger button */}
      <button
        type="button"
        data-tutorial-trigger="true"
        onClick={handleToggleExpanded}
        className={`
          group relative w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-600
          hover:from-blue-600 hover:to-purple-700 text-white rounded-full
          shadow-lg hover:shadow-xl transition-all duration-300
          flex items-center justify-center min-h-[48px] min-w-[48px]
          ${isExpanded ? 'rotate-180' : 'hover:scale-110'}
          ${state.isActive ? 'ring-4 ring-blue-200 ring-opacity-50' : ''}
        `}
        style={{
          touchAction: 'manipulation',
          WebkitTapHighlightColor: 'transparent',
          userSelect: 'none',
          zIndex: 9995,
          cursor: 'pointer'
        }}
        title={isExpanded ? 'Close tutorial menu' : 'Open tutorial guide'}
      >
        {isExpanded ? (
          <ChevronDown size={20} className="transition-transform duration-300" />
        ) : (
          <HelpCircle size={20} className="transition-transform duration-300" />
        )}
        
        {/* Progress indicator */}
        {progress.percentage > 0 && !isExpanded && (
          <div className="absolute -top-1 -right-1 w-6 h-6 bg-white rounded-full flex items-center justify-center shadow-md">
            <span className="text-xs font-bold text-gray-700">
              {progress.percentage}%
            </span>
          </div>
        )}

        {/* Pulse animation for new users */}
        {!hasInteracted && !isCategoryComplete('welcome') && (
          <div className="absolute inset-0 rounded-full bg-blue-400 animate-ping opacity-30" />
        )}
      </button>

      {/* Tutorial Settings Modal */}
      <TutorialSettingsModal />
    </div>
  )
}
